import React, { useState, useRef, useEffect } from "react";
import {
    Box,
    Container,
    Typography,
    Paper,
    Grid2 as Grid,
    Card,
    CardContent,
    Button,
    Select,
    MenuItem,
    FormControl,
    InputLabel,
    TextField,
    Switch,
    FormControlLabel,
    Chip,
    Alert,
    CircularProgress,
    Divider,
    IconButton,
    Tooltip,
} from "@mui/material";
import {
    CameraAlt as CameraIcon,
    CloudUpload as UploadIcon,
    Settings as SettingsIcon,
    PlayArrow as PlayIcon,
    Stop as StopIcon,
    Refresh as RefreshIcon,
    Info as InfoIcon,
    Wifi as MqttIcon,
} from "@mui/icons-material";
import { motion } from "framer-motion";
import { useDocumentTitle } from "../../../hooks/useDocumentTitle";
import Footer from "../../Common/Footer";

// Import TensorFlow.js components
import TensorFlowCamera from "./components/TensorFlowCamera";
import ModelManager from "./components/ModelManager";
import OutputDisplay from "./components/OutputDisplay";
import MQTTClient from "./components/MQTTClient";

const BeeAI = ({ user, setUser }) => {
    useDocumentTitle("BeE AI - TensorFlow.js Integration");

    // State management
    const [isModelLoaded, setIsModelLoaded] = useState(false);
    const [isDetecting, setIsDetecting] = useState(false);
    const [currentModel, setCurrentModel] = useState("mobilenet");
    const [modelUrl, setModelUrl] = useState("");
    const [predictions, setPredictions] = useState([]);
    const [mqttConnected, setMqttConnected] = useState(false);
    const [cameraError, setCameraError] = useState(null);
    const [modelError, setModelError] = useState(null);
    const [detectionSettings, setDetectionSettings] = useState({
        confidenceThreshold: 0.5,
        maxPredictions: 5,
        detectionInterval: 1000,
        showBoundingBoxes: true,
        enableMQTT: true,
    });

    // Refs
    const videoRef = useRef(null);
    const canvasRef = useRef(null);
    const mqttClientRef = useRef(null);

    // Animation variants
    const containerVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.6,
                staggerChildren: 0.1,
            },
        },
    };

    const itemVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0 },
    };

    // Handle model selection change
    const handleModelChange = (event) => {
        setCurrentModel(event.target.value);
        setIsModelLoaded(false);
        setPredictions([]);
    };

    // Handle detection toggle
    const handleDetectionToggle = () => {
        if (isDetecting) {
            setIsDetecting(false);
        } else {
            if (isModelLoaded) {
                setIsDetecting(true);
            } else {
                setModelError("Please load a model first");
            }
        }
    };

    // Handle settings change
    const handleSettingsChange = (setting, value) => {
        setDetectionSettings((prev) => ({
            ...prev,
            [setting]: value,
        }));
    };

    // Get user's IP address for MQTT topic
    const [userIP, setUserIP] = useState("unknown");
    useEffect(() => {
        fetch("https://api.ipify.org?format=json")
            .then((response) => response.json())
            .then((data) => setUserIP(data.ip))
            .catch(() => setUserIP("unknown"));
    }, []);

    return (
        <Box
            sx={{
                minHeight: "100vh",
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                display: "flex",
                flexDirection: "column",
            }}
        >
            <Container maxWidth="xl" sx={{ flexGrow: 1, py: 4 }}>
                <motion.div variants={containerVariants} initial="hidden" animate="visible">
                    {/* Header */}
                    <motion.div variants={itemVariants}>
                        <Paper
                            elevation={24}
                            sx={{
                                p: 4,
                                mb: 4,
                                background: "rgba(255, 255, 255, 0.1)",
                                backdropFilter: "blur(10px)",
                                borderRadius: "20px",
                                border: "1px solid rgba(255, 255, 255, 0.2)",
                                textAlign: "center",
                            }}
                        >
                            <Typography
                                variant="h3"
                                component="h1"
                                sx={{
                                    fontWeight: "bold",
                                    background: "linear-gradient(45deg,rgb(151, 254, 107) 30%, #FF8E53 90%)",
                                    backgroundClip: "text",
                                    WebkitBackgroundClip: "text",
                                    WebkitTextFillColor: "transparent",
                                    // mb: 2,
                                }}
                            >
                                🤖 BeE AI Studio
                            </Typography>
                            {/* <Typography
                                variant="h6"
                                sx={{
                                    color: "rgba(255, 255, 255, 0.8)",
                                    mb: 2,
                                }}
                            >
                                TensorFlow.js Object Detection & Classification
                            </Typography>
                            <Box sx={{ display: "flex", justifyContent: "center", gap: 2, flexWrap: "wrap" }}>
                                <Chip
                                    icon={<CameraIcon />}
                                    label="Real-time Detection"
                                    color="primary"
                                    variant="outlined"
                                />
                                <Chip
                                    icon={<UploadIcon />}
                                    label="Teachable Machine"
                                    color="secondary"
                                    variant="outlined"
                                />
                                <Chip icon={<MqttIcon />} label="MQTT Integration" color="success" variant="outlined" />
                            </Box> */}
                        </Paper>
                    </motion.div>

                    {/* Main Content Grid */}
                    <Grid container spacing={3}>
                        {/* Left Panel - Camera and Detection */}
                        <Grid size={{ xs: 12, lg: 8 }}>
                            <motion.div variants={itemVariants}>
                                <TensorFlowCamera
                                    ref={videoRef}
                                    canvasRef={canvasRef}
                                    currentModel={currentModel}
                                    modelUrl={modelUrl}
                                    isDetecting={isDetecting}
                                    setIsDetecting={setIsDetecting}
                                    isModelLoaded={isModelLoaded}
                                    setIsModelLoaded={setIsModelLoaded}
                                    predictions={predictions}
                                    setPredictions={setPredictions}
                                    detectionSettings={detectionSettings}
                                    cameraError={cameraError}
                                    setCameraError={setCameraError}
                                    modelError={modelError}
                                    setModelError={setModelError}
                                    mqttClientRef={mqttClientRef}
                                    userIP={userIP}
                                />
                            </motion.div>
                            {/* Output Display */}
                            {/* <motion.div variants={itemVariants}> */}
                            <OutputDisplay
                                predictions={predictions}
                                detectionSettings={detectionSettings}
                                onSettingsChange={handleSettingsChange}
                            />
                            {/* </motion.div> */}
                        </Grid>

                        {/* Right Panel - Controls and Output */}
                        <Grid size={{ xs: 12, lg: 4 }}>
                            <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
                                {/* Model Manager */}
                                <motion.div variants={itemVariants}>
                                    <ModelManager
                                        currentModel={currentModel}
                                        setCurrentModel={setCurrentModel}
                                        modelUrl={modelUrl}
                                        setModelUrl={setModelUrl}
                                        isModelLoaded={isModelLoaded}
                                        setIsModelLoaded={setIsModelLoaded}
                                        modelError={modelError}
                                        setModelError={setModelError}
                                        onModelChange={handleModelChange}
                                    />
                                </motion.div>

                                {/* Detection Controls */}
                                <motion.div variants={itemVariants}>
                                    <Paper
                                        elevation={12}
                                        sx={{
                                            p: 3,
                                            background: "rgba(255, 255, 255, 0.05)",
                                            backdropFilter: "blur(10px)",
                                            borderRadius: "16px",
                                            border: "1px solid rgba(255, 255, 255, 0.1)",
                                        }}
                                    >
                                        <Typography variant="h6" sx={{ color: "white", mb: 2 }}>
                                            Detection Controls
                                        </Typography>
                                        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                                            <Button
                                                variant="contained"
                                                size="large"
                                                startIcon={isDetecting ? <StopIcon /> : <PlayIcon />}
                                                onClick={handleDetectionToggle}
                                                disabled={!isModelLoaded}
                                                sx={{
                                                    background: isDetecting
                                                        ? "linear-gradient(45deg, #f44336 30%, #ff5722 90%)"
                                                        : "linear-gradient(45deg, #4caf50 30%, #8bc34a 90%)",
                                                    "&:hover": {
                                                        background: isDetecting
                                                            ? "linear-gradient(45deg, #d32f2f 30%, #f44336 90%)"
                                                            : "linear-gradient(45deg, #388e3c 30%, #4caf50 90%)",
                                                    },
                                                }}
                                            >
                                                {isDetecting ? "Stop Detection" : "Start Detection"}
                                            </Button>
                                        </Box>
                                    </Paper>
                                </motion.div>

                                {/* MQTT Client */}
                                <motion.div variants={itemVariants}>
                                    <MQTTClient
                                        ref={mqttClientRef}
                                        userIP={userIP}
                                        mqttConnected={mqttConnected}
                                        setMqttConnected={setMqttConnected}
                                        enabled={detectionSettings.enableMQTT}
                                    />
                                </motion.div>
                            </Box>
                        </Grid>
                    </Grid>
                </motion.div>
            </Container>
            {/* <Footer /> */}
        </Box>
    );
};

export default BeeAI;
