# BeE AI Studio - TensorFlow.js Integration

BeE AI Studio là một trang web tích hợp TensorFlow.js để thực hiện object classification và object detection với khả năng gửi kết quả qua MQTT.

## 🚀 Tính năng chính

### 1. **Object Classification & Detection**
- **MobileNet**: Phân loại đối tượng với hơn 1000 lớp
- **COCO-SSD**: Phát hiện đối tượng với bounding boxes cho 80 đối tượng phổ biến
- **Teachable Machine**: Hỗ trợ model tùy chỉnh từ Google Teachable Machine

### 2. **Camera Interface**
- Truy cập camera real-time
- Hiển thị video stream với overlay predictions
- Vẽ bounding boxes cho object detection
- Điều chỉnh confidence threshold và số lượng predictions

### 3. **MQTT Integration**
- Kết nối MQTT broker (mặc định: beeblock.vn:8883)
- <PERSON><PERSON>i kết quả AI detection đến topic: `bee/AI/<ip>/`
- <PERSON><PERSON><PERSON> hình SSL/TLS, username, password
- Hiển thị lịch sử tin nhắn

### 4. **Model Management**
- Chọn giữa các model có sẵn
- Load model từ Teachable Machine URL
- Upload model files (tính năng sắp tới)
- Validation URL Teachable Machine

## 📁 Cấu trúc thư mục

```
BeeAI/
├── BeeAI.jsx                 # Component chính
├── components/
│   ├── TensorFlowCamera.jsx  # Camera và AI detection
│   ├── ModelManager.jsx      # Quản lý models
│   ├── OutputDisplay.jsx     # Hiển thị kết quả
│   └── MQTTClient.jsx        # MQTT client
└── README.md                 # Tài liệu này
```

## 🔧 Cài đặt và sử dụng

### 1. **Truy cập trang AI**
```
http://localhost:5173/play/AI
```

### 2. **Chọn Model**
- **MobileNet**: Phân loại đối tượng tổng quát
- **COCO-SSD**: Phát hiện đối tượng với bounding box
- **Teachable Machine**: Model tùy chỉnh

### 3. **Cấu hình Teachable Machine**
```
URL mẫu: https://teachablemachine.withgoogle.com/models/86y3FI4B8/
```

### 4. **Kết nối MQTT**
```javascript
Broker: beeblock.vn
Port: 8883
Username: bee_user
Password: bee_pass
SSL: Enabled
```

### 5. **Bắt đầu Detection**
1. Chọn model
2. Cho phép truy cập camera
3. Kết nối MQTT (tùy chọn)
4. Nhấn "Start Detection"

## 📊 Output Format

### MQTT Message Structure
```json
{
  "timestamp": "2024-07-24T14:30:00.000Z",
  "model": "cocossd",
  "predictions": [
    {
      "class": "person",
      "confidence": 0.95,
      "bbox": [100, 50, 200, 300],
      "position": {
        "x": 200,
        "y": 200
      }
    }
  ],
  "ip": "*************"
}
```

### Object Detection Output
- **class**: Tên lớp đối tượng
- **confidence**: Độ tin cậy (0-1)
- **bbox**: Bounding box [x, y, width, height]
- **position**: Tọa độ trung tâm {x, y}

## ⚙️ Cấu hình Detection

### Detection Settings
- **Confidence Threshold**: 0.1 - 1.0 (mặc định: 0.5)
- **Max Predictions**: 1 - 10 (mặc định: 5)
- **Detection Interval**: 500 - 5000ms (mặc định: 1000ms)
- **Show Bounding Boxes**: Hiển thị khung bao quanh đối tượng
- **Enable MQTT**: Bật/tắt gửi kết quả qua MQTT

## 🔗 Dependencies

### Runtime Dependencies
- **TensorFlow.js**: Loaded via CDN
- **@tensorflow-models/mobilenet**: Classification model
- **@tensorflow-models/coco-ssd**: Object detection model
- **@teachablemachine/image**: Teachable Machine support
- **mqtt**: MQTT client
- **react-dropzone**: File upload interface

### CDN Libraries
```javascript
// TensorFlow.js Core
https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@3.21.0/dist/tf.min.js

// Models
https://cdn.jsdelivr.net/npm/@tensorflow-models/mobilenet@2.1.1/dist/mobilenet.min.js
https://cdn.jsdelivr.net/npm/@tensorflow-models/coco-ssd@2.2.2/dist/coco-ssd.min.js
https://cdn.jsdelivr.net/npm/@teachablemachine/image@0.8.6/dist/teachablemachine-image.min.js
```

## 🎯 Use Cases

### 1. **Object Classification**
- Nhận diện đối tượng trong hình ảnh
- Phân loại sản phẩm
- Kiểm tra chất lượng

### 2. **Object Detection**
- Đếm số lượng đối tượng
- Theo dõi vị trí đối tượng
- Hệ thống an ninh

### 3. **IoT Integration**
- Gửi dữ liệu AI qua MQTT
- Tích hợp với hệ thống IoT
- Điều khiển thiết bị dựa trên AI

## 🔧 Troubleshooting

### Camera Issues
```javascript
// Lỗi: Camera không truy cập được
// Giải pháp: Kiểm tra quyền camera trong browser
navigator.mediaDevices.getUserMedia({video: true})
```

### Model Loading Issues
```javascript
// Lỗi: Model không load được
// Giải pháp: Kiểm tra kết nối internet và URL model
```

### MQTT Connection Issues
```javascript
// Lỗi: MQTT không kết nối được
// Giải pháp: Kiểm tra broker URL, port, credentials
```

## 📝 Sample Teachable Machine URLs

```
BeE Board Detection:
https://teachablemachine.withgoogle.com/models/86y3FI4B8/

Rock Paper Scissors:
https://teachablemachine.withgoogle.com/models/rps/
```

## 🚀 Future Features

- [ ] Model file upload support
- [ ] Custom model training interface
- [ ] Real-time performance metrics
- [ ] Video recording with annotations
- [ ] Batch processing mode
- [ ] Advanced MQTT topics configuration
- [ ] WebRTC streaming support

## 📞 Support

Để được hỗ trợ, vui lòng liên hệ:
- Email: <EMAIL>
- Website: https://beeblock.vn
- Documentation: https://docs.beeblock.vn
