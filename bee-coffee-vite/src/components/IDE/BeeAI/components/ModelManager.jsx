import React, { useState, useRef } from "react";
import {
    Box,
    Paper,
    Typography,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    TextField,
    Button,
    Alert,
    Chip,
    Divider,
    IconButton,
    Tooltip,
    LinearProgress,
} from "@mui/material";
import {
    CloudUpload as UploadIcon,
    Link as LinkIcon,
    CheckCircle as CheckIcon,
    Error as ErrorIcon,
    Refresh as RefreshIcon,
    Info as InfoIcon,
} from "@mui/icons-material";
import { useDropzone } from "react-dropzone";

const ModelManager = ({
    currentModel,
    setCurrentModel,
    modelUrl,
    setModelUrl,
    isModelLoaded,
    setIsModelLoaded,
    modelError,
    setModelError,
    onModelChange,
}) => {
    const [isLoading, setIsLoading] = useState(false);
    const [uploadedFiles, setUploadedFiles] = useState([]);
    const [urlInput, setUrlInput] = useState("");
    const fileInputRef = useRef(null);

    // Predefined models
    const predefinedModels = [
        {
            value: "mobilenet",
            label: "MobileNet (Classification)",
            description: "General object classification with 1000+ classes",
            type: "classification",
        },
        {
            value: "cocossd",
            label: "COCO-SSD (Detection)",
            description: "Object detection with bounding boxes for 80 common objects",
            type: "detection",
        },
        {
            value: "teachable",
            label: "Teachable Machine (Custom)",
            description: "Custom model from Google Teachable Machine",
            type: "custom",
        },
    ];

    // Sample Teachable Machine URLs for testing
    const sampleUrls = [
        {
            name: "BeE Board vs Cuộn chì",
            url: "https://teachablemachine.withgoogle.com/models/86y3FI4B8/",
        },
        {
            name: "Rock Paper Scissors",
            url: "https://teachablemachine.withgoogle.com/models/rps/",
        },
    ];

    // Handle model selection
    const handleModelSelect = (event) => {
        const selectedModel = event.target.value;
        setCurrentModel(selectedModel);
        onModelChange(event);

        // Clear custom model URL if switching away from teachable machine
        if (selectedModel !== "teachable") {
            setModelUrl("");
            setUrlInput("");
        }
    };

    // Handle URL input
    const handleUrlSubmit = () => {
        if (urlInput.trim()) {
            setModelUrl(urlInput.trim());
            setIsModelLoaded(false);
        }
    };

    // Handle sample URL selection
    const handleSampleUrl = (url) => {
        setUrlInput(url);
        setModelUrl(url);
        setIsModelLoaded(false);
    };

    // File upload handling
    const onDrop = (acceptedFiles) => {
        setUploadedFiles(acceptedFiles);

        // For now, we'll show an info message about file upload
        // In a real implementation, you'd upload these files to a server
        // and get back a URL to use with the model
        setModelError("File upload feature coming soon. Please use URL method for now.");
    };

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        accept: {
            "application/json": [".json"],
            "application/octet-stream": [".bin"],
        },
        multiple: true,
    });

    // Validate Teachable Machine URL
    const validateTeachableMachineUrl = (url) => {
        const teachableMachinePattern = /^https:\/\/teachablemachine\.withgoogle\.com\/models\/[a-zA-Z0-9_-]+\/?$/;
        return teachableMachinePattern.test(url);
    };

    // Get model status
    const getModelStatus = () => {
        if (modelError) {
            return { status: "error", message: modelError };
        }
        if (isLoading) {
            return { status: "loading", message: "Loading model..." };
        }
        if (isModelLoaded) {
            return { status: "success", message: "Model loaded successfully" };
        }
        return { status: "idle", message: "Select a model to get started" };
    };

    const modelStatus = getModelStatus();

    return (
        <Paper
            elevation={12}
            sx={{
                p: 3,
                background: "rgba(255, 255, 255, 0.05)",
                backdropFilter: "blur(10px)",
                borderRadius: "16px",
                border: "1px solid rgba(255, 255, 255, 0.1)",
            }}
        >
            <Typography variant="h6" sx={{ color: "white", mb: 2 }}>
                🧠 Model Manager
            </Typography>

            {/* Model Status */}
            <Box sx={{ mb: 3 }}>
                {modelStatus.status === "error" && (
                    <Alert severity="error" icon={<ErrorIcon />}>
                        {modelStatus.message}
                    </Alert>
                )}
                {modelStatus.status === "loading" && (
                    <Box>
                        <Alert severity="info" icon={<InfoIcon />}>
                            {modelStatus.message}
                        </Alert>
                        <LinearProgress sx={{ mt: 1 }} />
                    </Box>
                )}
                {modelStatus.status === "success" && (
                    <Alert severity="success" icon={<CheckIcon />}>
                        {modelStatus.message}
                    </Alert>
                )}
                {modelStatus.status === "idle" && (
                    <Alert severity="info" icon={<InfoIcon />}>
                        {modelStatus.message}
                    </Alert>
                )}
            </Box>

            {/* Model Selection */}
            <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>Select AI Model</InputLabel>
                <Select
                    value={currentModel}
                    onChange={handleModelSelect}
                    sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover .MuiOutlinedInput-notchedOutline": {
                            borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                        "& .MuiSvgIcon-root": {
                            color: "white",
                        },
                    }}
                >
                    {predefinedModels.map((model) => (
                        <MenuItem key={model.value} value={model.value}>
                            <Box>
                                <Typography variant="body1">{model.label}</Typography>
                                <Typography variant="caption" color="text.secondary" sx={{ color: "white" }}>
                                    {model.description}
                                </Typography>
                            </Box>
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>

            {/* Model Type Indicator */}
            <Box sx={{ mb: 3 }}>
                <Chip
                    label={predefinedModels.find((m) => m.value === currentModel)?.type || "unknown"}
                    color={currentModel === "cocossd" ? "secondary" : "primary"}
                    size="small"
                    sx={{ textTransform: "capitalize" }}
                />
            </Box>

            {/* Teachable Machine URL Input */}
            {currentModel === "teachable" && (
                <Box sx={{ mb: 3 }}>
                    <Divider sx={{ mb: 2, borderColor: "rgba(255, 255, 255, 0.1)" }} />
                    <Typography variant="subtitle2" sx={{ color: "white", mb: 2 }}>
                        Teachable Machine Model URL
                    </Typography>

                    {/* Sample URLs */}
                    <Box sx={{ mb: 2 }}>
                        <Typography
                            variant="caption"
                            sx={{ color: "rgba(255, 255, 255, 0.7)", mb: 1, display: "block" }}
                        >
                            Try these sample models:
                        </Typography>
                        {sampleUrls.map((sample, index) => (
                            <Chip
                                key={index}
                                label={sample.name}
                                onClick={() => handleSampleUrl(sample.url)}
                                sx={{ mr: 1, mb: 1, cursor: "pointer", color: "white" }}
                                size="small"
                                variant="outlined"
                            />
                        ))}
                    </Box>

                    {/* URL Input */}
                    <Box sx={{ display: "flex", gap: 1, mb: 2 }}>
                        <TextField
                            fullWidth
                            placeholder="https://teachablemachine.withgoogle.com/models/YOUR_MODEL_ID/"
                            value={urlInput}
                            onChange={(e) => setUrlInput(e.target.value)}
                            sx={{
                                "& .MuiOutlinedInput-root": {
                                    color: "white",
                                    "& fieldset": {
                                        borderColor: "rgba(255, 255, 255, 0.3)",
                                    },
                                    "&:hover fieldset": {
                                        borderColor: "rgba(255, 255, 255, 0.5)",
                                    },
                                },
                                "& .MuiInputBase-input::placeholder": {
                                    color: "rgba(255, 255, 255, 0.5)",
                                    opacity: 1,
                                },
                            }}
                        />
                        <Button
                            variant="contained"
                            onClick={handleUrlSubmit}
                            disabled={!urlInput.trim() || !validateTeachableMachineUrl(urlInput)}
                            startIcon={<LinkIcon />}
                        >
                            Load
                        </Button>
                    </Box>

                    {urlInput && !validateTeachableMachineUrl(urlInput) && (
                        <Alert severity="warning" sx={{ mb: 2 }}>
                            Please enter a valid Teachable Machine URL
                        </Alert>
                    )}

                    {/* File Upload (Future Feature) */}
                    <Divider sx={{ my: 2, borderColor: "rgba(255, 255, 255, 0.1)" }} />
                    <Typography variant="caption" sx={{ color: "rgba(255, 255, 255, 0.7)", mb: 1, display: "block" }}>
                        Or upload model files (Coming Soon):
                    </Typography>
                    <Box
                        {...getRootProps()}
                        sx={{
                            border: "2px dashed rgba(255, 255, 255, 0.3)",
                            borderRadius: "8px",
                            p: 2,
                            textAlign: "center",
                            cursor: "pointer",
                            opacity: 0.5,
                            "&:hover": {
                                borderColor: "rgba(255, 255, 255, 0.5)",
                            },
                        }}
                    >
                        <input {...getInputProps()} />
                        <UploadIcon sx={{ color: "rgba(255, 255, 255, 0.7)", mb: 1 }} />
                        <Typography variant="body2" sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                            {isDragActive ? "Drop files here..." : "Drag & drop model files or click to browse"}
                        </Typography>
                        <Typography variant="caption" sx={{ color: "rgba(255, 255, 255, 0.5)" }}>
                            Supports: model.json, weights.bin
                        </Typography>
                    </Box>
                </Box>
            )}

            {/* Current Model Info */}
            {modelUrl && currentModel === "teachable" && (
                <Box sx={{ mt: 2 }}>
                    <Typography variant="caption" sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                        Current Model URL:
                    </Typography>
                    <Typography variant="body2" sx={{ color: "white", wordBreak: "break-all" }}>
                        {modelUrl}
                    </Typography>
                </Box>
            )}
        </Paper>
    );
};

export default ModelManager;
