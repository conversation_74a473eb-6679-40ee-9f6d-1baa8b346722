import React, { useState } from "react";
import {
    Box,
    Paper,
    Typography,
    List,
    ListItem,
    ListItemText,
    Chip,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    Slider,
    FormControlLabel,
    Switch,
    TextField,
    Divider,
    Badge,
    LinearProgress,
} from "@mui/material";
import {
    ExpandMore as ExpandMoreIcon,
    Visibility as VisibilityIcon,
    Settings as SettingsIcon,
    TrendingUp as TrendingUpIcon,
    LocationOn as LocationIcon,
    Timer as TimerIcon,
} from "@mui/icons-material";

const OutputDisplay = ({ predictions, detectionSettings, onSettingsChange }) => {
    const [settingsExpanded, setSettingsExpanded] = useState(false);

    // Format confidence percentage
    const formatConfidence = (confidence) => {
        return Math.round(confidence * 100);
    };

    // Format position for display
    const formatPosition = (bbox) => {
        if (!bbox) return null;
        const centerX = Math.round(bbox[0] + bbox[2] / 2);
        const centerY = Math.round(bbox[1] + bbox[3] / 2);
        return { x: centerX, y: centerY };
    };

    // Get confidence color
    const getConfidenceColor = (confidence) => {
        if (confidence >= 0.8) return "success";
        if (confidence >= 0.6) return "warning";
        return "error";
    };

    // Handle settings change
    const handleSettingChange = (setting, value) => {
        onSettingsChange(setting, value);
    };

    return (
        <Paper
            elevation={12}
            sx={{
                background: "rgba(255, 255, 255, 0.05)",
                backdropFilter: "blur(10px)",
                borderRadius: "16px",
                border: "1px solid rgba(255, 255, 255, 0.1)",
                overflow: "hidden",
                mt: 3,
            }}
        >
            {/* Header */}
            <Box sx={{ p: 3, pb: 2 }}>
                <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                    <Typography variant="h6" sx={{ color: "white" }}>
                        📊 Detection Results
                    </Typography>
                    <Badge badgeContent={predictions.length} color="primary">
                        <VisibilityIcon sx={{ color: "white" }} />
                    </Badge>
                </Box>
            </Box>

            {/* Predictions List */}
            <Box sx={{ px: 3, pb: 2, maxHeight: "300px", overflowY: "auto" }}>
                {predictions.length === 0 ? (
                    <Box sx={{ textAlign: "center", py: 4 }}>
                        <Typography variant="body2" sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                            No objects detected
                        </Typography>
                        <Typography variant="caption" sx={{ color: "rgba(255, 255, 255, 0.5)" }}>
                            Start detection to see results
                        </Typography>
                    </Box>
                ) : (
                    <List sx={{ p: 0 }}>
                        {predictions.map((prediction, index) => {
                            const confidence = prediction.probability || prediction.score;
                            const className = prediction.class || prediction.className;
                            const position = formatPosition(prediction.bbox);

                            return (
                                <ListItem
                                    key={index}
                                    sx={{
                                        mb: 1,
                                        background: "rgba(255, 255, 255, 0.05)",
                                        borderRadius: "8px",
                                        border: "1px solid rgba(255, 255, 255, 0.1)",
                                    }}
                                >
                                    <ListItemText
                                        primary={
                                            <Box
                                                sx={{
                                                    display: "flex",
                                                    justifyContent: "space-between",
                                                    alignItems: "center",
                                                    mb: 1,
                                                }}
                                            >
                                                <Typography variant="body1" sx={{ color: "white", fontWeight: "bold" }}>
                                                    {className}
                                                </Typography>
                                                <Chip
                                                    label={`${formatConfidence(confidence)}%`}
                                                    color={getConfidenceColor(confidence)}
                                                    size="small"
                                                />
                                            </Box>
                                        }
                                        secondary={
                                            <Box>
                                                {/* Confidence Bar */}
                                                <Box sx={{ mb: 1 }}>
                                                    <LinearProgress
                                                        variant="determinate"
                                                        value={confidence * 100}
                                                        sx={{
                                                            height: 6,
                                                            borderRadius: 3,
                                                            backgroundColor: "rgba(255, 255, 255, 0.1)",
                                                            "& .MuiLinearProgress-bar": {
                                                                backgroundColor:
                                                                    confidence >= 0.8
                                                                        ? "#4caf50"
                                                                        : confidence >= 0.6
                                                                          ? "#ff9800"
                                                                          : "#f44336",
                                                            },
                                                        }}
                                                    />
                                                </Box>

                                                {/* Position and Bounding Box Info */}
                                                {prediction.bbox && (
                                                    <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
                                                        {position && (
                                                            <Box
                                                                sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                                                            >
                                                                <LocationIcon
                                                                    sx={{
                                                                        fontSize: 14,
                                                                        color: "rgba(255, 255, 255, 0.7)",
                                                                    }}
                                                                />
                                                                <Typography
                                                                    variant="caption"
                                                                    sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                                                                >
                                                                    Position: ({position.x}, {position.y})
                                                                </Typography>
                                                            </Box>
                                                        )}
                                                        <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                                                            <Typography
                                                                variant="caption"
                                                                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                                                            >
                                                                Box: {Math.round(prediction.bbox[2])}×
                                                                {Math.round(prediction.bbox[3])}
                                                            </Typography>
                                                        </Box>
                                                    </Box>
                                                )}

                                                {/* Timestamp */}
                                                <Box sx={{ display: "flex", alignItems: "center", gap: 0.5, mt: 0.5 }}>
                                                    <TimerIcon
                                                        sx={{ fontSize: 12, color: "rgba(255, 255, 255, 0.5)" }}
                                                    />
                                                    <Typography
                                                        variant="caption"
                                                        sx={{ color: "rgba(255, 255, 255, 0.5)" }}
                                                    >
                                                        {new Date().toLocaleTimeString()}
                                                    </Typography>
                                                </Box>
                                            </Box>
                                        }
                                    />
                                </ListItem>
                            );
                        })}
                    </List>
                )}
            </Box>

            {/* Settings Panel */}
            <Accordion
                expanded={settingsExpanded}
                onChange={(event, isExpanded) => setSettingsExpanded(isExpanded)}
                sx={{
                    background: "rgba(255, 255, 255, 0.02)",
                    "&:before": { display: "none" },
                    boxShadow: "none",
                }}
            >
                <AccordionSummary
                    expandIcon={<ExpandMoreIcon sx={{ color: "white" }} />}
                    sx={{
                        borderTop: "1px solid rgba(255, 255, 255, 0.1)",
                        "& .MuiAccordionSummary-content": {
                            alignItems: "center",
                        },
                    }}
                >
                    <SettingsIcon sx={{ color: "white", mr: 1 }} />
                    <Typography sx={{ color: "white" }}>Detection Settings</Typography>
                </AccordionSummary>
                <AccordionDetails sx={{ pt: 0 }}>
                    <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
                        {/* Confidence Threshold */}
                        <Box>
                            <Typography variant="body2" sx={{ color: "white", mb: 1 }}>
                                Confidence Threshold: {Math.round(detectionSettings.confidenceThreshold * 100)}%
                            </Typography>
                            <Slider
                                value={detectionSettings.confidenceThreshold}
                                onChange={(e, value) => handleSettingChange("confidenceThreshold", value)}
                                min={0.1}
                                max={1.0}
                                step={0.05}
                                sx={{
                                    color: "primary.main",
                                    "& .MuiSlider-thumb": {
                                        backgroundColor: "primary.main",
                                    },
                                    "& .MuiSlider-track": {
                                        backgroundColor: "primary.main",
                                    },
                                    "& .MuiSlider-rail": {
                                        backgroundColor: "rgba(255, 255, 255, 0.3)",
                                    },
                                }}
                            />
                        </Box>

                        {/* Max Predictions */}
                        <Box>
                            <Typography variant="body2" sx={{ color: "white", mb: 1 }}>
                                Max Predictions: {detectionSettings.maxPredictions}
                            </Typography>
                            <Slider
                                value={detectionSettings.maxPredictions}
                                onChange={(e, value) => handleSettingChange("maxPredictions", value)}
                                min={1}
                                max={10}
                                step={1}
                                marks
                                sx={{
                                    color: "secondary.main",
                                    "& .MuiSlider-thumb": {
                                        backgroundColor: "secondary.main",
                                    },
                                    "& .MuiSlider-track": {
                                        backgroundColor: "secondary.main",
                                    },
                                    "& .MuiSlider-rail": {
                                        backgroundColor: "rgba(255, 255, 255, 0.3)",
                                    },
                                }}
                            />
                        </Box>

                        {/* Detection Interval */}
                        <Box>
                            <Typography variant="body2" sx={{ color: "white", mb: 1 }}>
                                Detection Interval: {detectionSettings.detectionInterval}ms
                            </Typography>
                            <Slider
                                value={detectionSettings.detectionInterval}
                                onChange={(e, value) => handleSettingChange("detectionInterval", value)}
                                min={500}
                                max={5000}
                                step={250}
                                sx={{
                                    color: "success.main",
                                    "& .MuiSlider-thumb": {
                                        backgroundColor: "success.main",
                                    },
                                    "& .MuiSlider-track": {
                                        backgroundColor: "success.main",
                                    },
                                    "& .MuiSlider-rail": {
                                        backgroundColor: "rgba(255, 255, 255, 0.3)",
                                    },
                                }}
                            />
                        </Box>

                        <Divider sx={{ borderColor: "rgba(255, 255, 255, 0.1)" }} />

                        {/* Toggle Settings */}
                        <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
                            <FormControlLabel
                                control={
                                    <Switch
                                        checked={detectionSettings.showBoundingBoxes}
                                        onChange={(e) => handleSettingChange("showBoundingBoxes", e.target.checked)}
                                        sx={{
                                            "& .MuiSwitch-switchBase.Mui-checked": {
                                                color: "primary.main",
                                            },
                                            "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track": {
                                                backgroundColor: "primary.main",
                                            },
                                        }}
                                    />
                                }
                                label={
                                    <Typography variant="body2" sx={{ color: "white" }}>
                                        Show Bounding Boxes
                                    </Typography>
                                }
                            />
                            <FormControlLabel
                                control={
                                    <Switch
                                        checked={detectionSettings.enableMQTT}
                                        onChange={(e) => handleSettingChange("enableMQTT", e.target.checked)}
                                        sx={{
                                            "& .MuiSwitch-switchBase.Mui-checked": {
                                                color: "success.main",
                                            },
                                            "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track": {
                                                backgroundColor: "success.main",
                                            },
                                        }}
                                    />
                                }
                                label={
                                    <Typography variant="body2" sx={{ color: "white" }}>
                                        Enable MQTT Output
                                    </Typography>
                                }
                            />
                        </Box>
                    </Box>
                </AccordionDetails>
            </Accordion>
        </Paper>
    );
};

export default OutputDisplay;
