<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="brainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#4ECDC4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#45B7D1;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="circuitGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD93D;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6BCF7F;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="100" cy="100" r="95" fill="url(#brainGradient)" opacity="0.1"/>
  
  <!-- Brain outline -->
  <path d="M60 80 C50 60, 80 50, 100 60 C120 50, 150 60, 140 80 C150 90, 140 110, 130 120 C140 140, 120 150, 100 140 C80 150, 60 140, 70 120 C60 110, 50 90, 60 80 Z" 
        fill="url(#brainGradient)" 
        stroke="#2C3E50" 
        stroke-width="3"/>
  
  <!-- Brain divisions -->
  <path d="M100 60 Q100 80, 100 140" 
        stroke="#2C3E50" 
        stroke-width="2" 
        fill="none"/>
  
  <!-- Circuit patterns -->
  <g stroke="url(#circuitGradient)" stroke-width="2" fill="none">
    <!-- Left hemisphere circuits -->
    <circle cx="80" cy="85" r="8" opacity="0.8"/>
    <circle cx="75" cy="105" r="6" opacity="0.8"/>
    <circle cx="85" cy="120" r="7" opacity="0.8"/>
    
    <!-- Right hemisphere circuits -->
    <circle cx="120" cy="85" r="8" opacity="0.8"/>
    <circle cx="125" cy="105" r="6" opacity="0.8"/>
    <circle cx="115" cy="120" r="7" opacity="0.8"/>
    
    <!-- Connecting lines -->
    <line x1="80" y1="85" x2="75" y2="105" opacity="0.6"/>
    <line x1="75" y1="105" x2="85" y2="120" opacity="0.6"/>
    <line x1="120" y1="85" x2="125" y2="105" opacity="0.6"/>
    <line x1="125" y1="105" x2="115" y2="120" opacity="0.6"/>
    <line x1="80" y1="85" x2="120" y2="85" opacity="0.6"/>
  </g>
  
  <!-- Neural nodes -->
  <g fill="#FFD93D">
    <circle cx="80" cy="85" r="3"/>
    <circle cx="75" cy="105" r="2"/>
    <circle cx="85" cy="120" r="2.5"/>
    <circle cx="120" cy="85" r="3"/>
    <circle cx="125" cy="105" r="2"/>
    <circle cx="115" cy="120" r="2.5"/>
    <circle cx="100" cy="100" r="4"/>
  </g>
  
  <!-- AI text -->
  <text x="100" y="170" 
        font-family="Arial, sans-serif" 
        font-size="24" 
        font-weight="bold" 
        text-anchor="middle" 
        fill="#2C3E50">AI</text>
  
  <!-- Animated pulse effect -->
  <circle cx="100" cy="100" r="4" fill="#FFD93D" opacity="0.8">
    <animate attributeName="r" values="4;8;4" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.8;0.3;0.8" dur="2s" repeatCount="indefinite"/>
  </circle>
</svg>
